:root {
  /* Brand Colors */
  --color-primary: #D4941E;
  --color-secondary: #8B5A2B;

  /* Neutral Colors */
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-gray: #737373;

  /* Text Colors */
  --text-primary: #171717;
  --text-secondary: #525252;
  --text-inverse: #FFFFFF;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
}
body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  background-color: #F9F9F9;
  line-height: 1.6;
}

h1, h2, h3 {
  font-weight: 700;
  margin: 0 0 1rem 0;
}
.btn-primary {
  background: var(--color-primary);
  color: var(--text-inverse);
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  transition: background 0.3s ease;
}

.btn-primary:hover {
  background: #B8751A;
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--color-primary);
  padding: 0.6rem 1.2rem;
  border: 2px solid var(--color-primary);
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #FEF7E8;
}
nav {
  background-color: var(--color-white);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

nav .logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
}

nav ul {
  list-style: none;
  display: flex;
  gap: 1.5rem;
  margin: 0;
}

nav a {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  transition: color 0.2s ease;
}

nav a:hover {
  color: var(--color-primary);
}
.hero {
  background: linear-gradient(135deg, #FEF7E8, #FFFFFF);
  padding: 4rem 2rem;
  text-align: center;
}

.hero h1 {
  font-size: 2rem;
  color: var(--text-primary);
}

.hero p {
  color: var(--text-secondary);
  margin: 1rem 0;
}
