/* Nirmanah Design & Build - Color System */
:root {
  /* Primary Brand Colors */
  --color-primary: #D4941E;           /* Main bronze-orange */
  --color-primary-50: #FEF7E8;        /* Lightest tint */
  --color-primary-100: #FDF0D1;       /* Very light */
  --color-primary-200: #FAE0A3;       /* Light */
  --color-primary-300: #F6CD75;       /* Medium light */
  --color-primary-400: #EFBA47;       /* Medium */
  --color-primary-500: #D4941E;       /* Base primary */
  --color-primary-600: #B8751A;       /* Medium dark */
  --color-primary-700: #985616;       /* Dark */
  --color-primary-800: #7A4213;       /* Very dark */
  --color-primary-900: #5C3010;       /* Darkest */

  /* Secondary Colors (Darker Bronze) */
  --color-secondary: #8B5A2B;         /* Rich bronze from palette */
  --color-secondary-50: #F6F1EC;      
  --color-secondary-100: #EDE3D9;     
  --color-secondary-200: #DBC7B3;     
  --color-secondary-300: #C9AB8D;     
  --color-secondary-400: #B78F67;     
  --color-secondary-500: #8B5A2B;     /* Base secondary */
  --color-secondary-600: #7D5127;     
  --color-secondary-700: #6F4823;     
  --color-secondary-800: #613F1F;     
  --color-secondary-900: #53361B;     

  /* Neutral Colors */
  --color-neutral-white: #FFFFFF;
  --color-neutral-50: #F9F9F9;        /* Off-white */
  --color-neutral-100: #F3F3F3;       /* Very light gray */
  --color-neutral-200: #E5E5E5;       /* Light gray */
  --color-neutral-300: #D4D4D4;       /* Medium light gray */
  --color-neutral-400: #A3A3A3;       /* Medium gray */
  --color-neutral-500: #737373;       /* Base gray */
  --color-neutral-600: #525252;       /* Medium dark gray */
  --color-neutral-700: #404040;       /* Dark gray */
  --color-neutral-800: #262626;       /* Very dark gray */
  --color-neutral-900: #171717;       /* Almost black */
  --color-neutral-black: #000000;

  /* Semantic Colors */
  --color-success: #059669;           /* Green for success states */
  --color-success-light: #D1FAE5;     
  --color-warning: #D97706;           /* Orange for warnings */
  --color-warning-light: #FED7AA;     
  --color-error: #DC2626;             /* Red for errors */
  --color-error-light: #FECACA;       
  --color-info: #2563EB;              /* Blue for information */
  --color-info-light: #DBEAFE;        

  /* Text Colors */
  --text-primary: #171717;            /* Primary text - dark */
  --text-secondary: #525252;          /* Secondary text - medium gray */
  --text-muted: #737373;              /* Muted text - light gray */
  --text-inverse: #FFFFFF;            /* Text on dark backgrounds */
  --text-brand: #D4941E;              /* Brand colored text */

  /* Background Colors */
  --bg-primary: #FFFFFF;              /* Main background */
  --bg-secondary: #F9F9F9;            /* Secondary background */
  --bg-tertiary: #F3F3F3;             /* Tertiary background */
  --bg-brand: #D4941E;                /* Brand background */
  --bg-brand-light: #FEF7E8;          /* Light brand background */
  --bg-dark: #171717;                 /* Dark background */

  /* Border Colors */
  --border-light: #E5E5E5;            /* Light borders */
  --border-medium: #D4D4D4;           /* Medium borders */
  --border-dark: #A3A3A3;             /* Dark borders */
  --border-brand: #D4941E;            /* Brand colored borders */

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-brand: 0 4px 14px 0 rgba(212, 148, 30, 0.25);
}

/* Typography */
body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
  background-color: var(--bg-secondary);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.nirmanah-font {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
}

.tagline {
  font-family: 'acumin-variable-concept', sans-serif;
  font-weight: 700;
  font-size: 12px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  color: var(--color-secondary-500);
}

/* Text Utilities */
.text-brand { color: var(--text-brand); }
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

/* Buttons */
.btn-primary, .quote-button {
  background-color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary-500));
  color: var(--text-inverse);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.btn-primary:hover, .quote-button:hover {
  background-color: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-brand);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: var(--bg-brand-light);
  color: var(--color-primary-600);
}

/* Navbar */
nav {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
}

/* Nav Links */
.nav-links {
  background-color: var(--color-neutral-900);
  border-radius: 9999px;
  padding: 0.5rem;
  box-shadow: var(--shadow-md);
}

.nav-item-active {
  background-color: var(--color-neutral-white);
  color: var(--text-primary);
  border-radius: 9999px;
  box-shadow: var(--shadow-sm);
}

.nav-item-hover {
  color: var(--color-neutral-300);
  border-radius: 9999px;
  transition: all 0.2s ease;
}

.nav-item-hover:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--color-primary);
}

/* Bronze Gradient */
.bronze-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary-700));
}

/* Cards */
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-md);
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card-header {
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

/* Service Cards */
.service-card {
  background-color: var(--bg-primary);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
  transition: all 0.2s ease;
  border-top: 4px solid var(--color-primary);
}

.service-card:hover {
  box-shadow: var(--shadow-md);
}

.service-card h3 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Architectural Element */
.arch-element {
  position: absolute;
  height: 3px;
  width: 20px;
  background-color: var(--color-secondary-500);
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 1px;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--bg-primary);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-lg);
  min-width: 240px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 0.5rem;
  border: 1px solid var(--border-light);
}

.dropdown:hover .dropdown-menu,
.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-light);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: var(--color-primary-50);
  color: var(--color-primary);
  padding-left: 1.25rem;
}

.dropdown-item:first-child {
  border-radius: 0.75rem 0.75rem 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 0.75rem 0.75rem;
}

/* Dropdown Arrow */
.dropdown-arrow {
  transition: transform 0.3s ease;
  margin-left: 0.25rem;
  display: inline-block;
}

.dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* Mobile Menu */
#mobile-menu {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
}

.mobile-menu-link {
  display: block;
  padding: 0.75rem 0;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.mobile-menu-link:hover, 
.mobile-menu-link:focus {
  color: var(--color-primary);
}

.mobile-menu-link.active {
  color: var(--text-primary);
  border-left: 4px solid var(--color-primary);
  padding-left: 1rem;
  background-color: var(--bg-tertiary);
}

/* Mobile Dropdown Styles */
.mobile-dropdown-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.mobile-dropdown-content.expanded {
  max-height: 300px;
}

.mobile-dropdown-item {
  padding: 0.5rem 0 0.5rem 1.5rem;
  color: var(--text-muted);
  font-size: 0.875rem;
  border-left: 2px solid transparent;
  transition: all 0.2s ease;
}

.mobile-dropdown-item:hover {
  color: var(--color-primary);
  border-left-color: var(--color-primary);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--bg-primary) 100%);
  padding: 4rem 2rem;
}

/* Footer */
.footer {
  background-color: var(--bg-dark);
  color: var(--text-inverse);
  padding: 4rem 2rem 2rem;
}

.footer a {
  color: var(--color-primary-300);
  transition: color 0.2s ease;
}

.footer a:hover {
  color: var(--color-primary);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dropdown-menu {
    display: none;
  }
  
  .nav-links {
    display: none;
  }
  
  .hero {
    padding: 2rem 1rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease forwards;
}