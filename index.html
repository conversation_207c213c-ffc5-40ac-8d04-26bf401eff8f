<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://use.typekit.net/oph7lmk.css"> <!-- Acumin Variable Concept via Adobe Fonts -->
  <link rel="stylesheet" href="../nirmanah-styles.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <title>Nirmanah Design & Build</title>
  <link rel="icon" href="favicon.ico" />
  <link style="stylesheet" href="style.css">
</head>
<body>
  <header>
    <!-- Navbar -->
  <nav class="flex items-center justify-between px-8 py-4 bg-white shadow-sm sticky top-0 z-50">
    <!-- Logo and Tagline -->
    <div class="flex items-center space-x-2">
      <img src="../Logonirmanah.png" alt="Nirmanah Logo" class="w-10 h-10 object-contain">
      <div class="flex flex-col">
        <span class="text-xl text-gray-900 nirmanah-font">nirmanah</span>
        <span class="tagline">design & build</span>
      </div>
    </div>
    
    <!-- Nav Links Container -->
    <ul class="hidden md:flex items-center space-x-1 nav-links">
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">Home</a>
      </li>
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">About</a>
      </li>
      <li class="dropdown">
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm flex items-center" 
           aria-haspopup="true" aria-expanded="false" role="button">
          Services
          <svg class="dropdown-arrow w-3 h-3 fill-current" viewBox="0 0 12 12">
            <path d="M6 8l4-4H2z"/>
          </svg>
        </a>
        <div class="dropdown-menu" role="menu">
          <a href="#" class="dropdown-item" role="menuitem">Design & Consultancy</a>
          <a href="#" class="dropdown-item" role="menuitem">Renovation & Remodeling</a>
          <a href="#" class="dropdown-item" role="menuitem">Landscaping & Outdoor Work</a>
          <a href="#" class="dropdown-item" role="menuitem">Turnkey Projects</a>
        </div>
      </li>
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">Portfolio</a>
      </li>
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">Contact</a>
      </li>
    </ul>
    
    <!-- Mobile Menu Button -->
    <div class="md:hidden">
      <button onclick="toggleMobileMenu()" class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200" aria-label="Toggle mobile menu">
        <svg id="menu-icon" class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
        <svg id="close-icon" class="w-6 h-6 text-gray-700 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <!-- Get a Quote Button -->
    <div class="hidden md:block">
      <button class="btn-primary">
        Get a Quote
      </button>
    </div>
  </nav>
  
  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden bg-white border-b shadow-lg hidden">
    <div class="px-8 py-4 space-y-4">
      <a href="#" class="block py-2 text-primary font-medium border-l-4 border-primary pl-4 bg-secondary mobile-menu-link active">
        Home
      </a>
      <a href="#" class="block py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link">About</a>
      
      <!-- Mobile Services Dropdown -->
      <div class="mobile-dropdown">
        <button onclick="toggleMobileDropdown()" class="flex items-center justify-between w-full py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link" aria-expanded="false">
          Services
          <svg id="mobile-dropdown-arrow" class="w-4 h-4 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
          </svg>
        </button>
        <div id="mobile-dropdown-content" class="mobile-dropdown-content">
          <a href="#" class="block mobile-dropdown-item">Design & Consultancy</a>
          <a href="#" class="block mobile-dropdown-item">Renovation & Remodeling</a>
          <a href="#" class="block mobile-dropdown-item">Landscaping & Outdoor Work</a>
          <a href="#" class="block mobile-dropdown-item">Turnkey Projects</a>
        </div>
      </div>
      
      <a href="#" class="block py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link">Portfolio</a>
      <a href="#" class="block py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link">Contact</a>
    <div class="container" style="display:flex;align-items:center;gap:1rem">
      <div class="brand">My Site</div>
      <nav class="nav-right" style="display:flex;gap:0.75rem;align-items:center">
        <a href="#services">Services</a>
        <a href="#contact">Contact</a>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="hero">
      <div>
        <h1>Simple, fast, and responsive HTML starter</h1>
        <p>Use this single-file template to quickly prototype a landing section, services, and a contact form. Clean, minimal CSS and mobile-friendly layout.</p>
        <p style="margin-top:1rem"><a class="btn" href="#contact">Get in touch</a></p>
      </div>

      <aside>
        <div class="card">
          <h3>Quick info</h3>
          <p style="margin:0;color:#555">This is an example aside card. Replace this content with client info, a photo, or a signup.</p>
        </div>
      </aside>
    </section>

    <section id="services">
      <h2>Services</h2>
      <div class="grid">
        <div class="card">
          <h3>Design</h3>
          <p>Create beautiful, functional layouts and user experiences.</p>
        </div>
        <div class="card">
          <h3>Build</h3>
          <p>Modern front-end and back-end development to ship products.</p>
        </div>
        <div class="card">
          <h3>Consult</h3>
          <p>Strategy, architecture reviews, and technical mentoring.</p>
        </div>
      </div>
    </section>

    <section id="contact" style="margin-top:1.5rem">
      <h2>Contact</h2>
      <div class="card">
        <form>
          <label>
            Name
            <input type="text" name="name" placeholder="Your name" />
          </label>
          <label>
            Email
            <input type="email" name="email" placeholder="<EMAIL>" />
          </label>
          <label>
            Message
            <textarea name="message" placeholder="Tell me about your project"></textarea>
          </label>
          <button type="submit" class="btn">Send message</button>
        </form>
      </div>
    </section>
  </main>

  <footer>
    <div class="container">© <span id="year"></span> My Site — Built with a simple HTML template</div>
  </footer>

  <script>
    // small JS niceties
    document.getElementById('year').textContent = new Date().getFullYear();

    // Basic form handling (example only - replace with proper backend)
    document.querySelector('form').addEventListener('submit', function(e){
      e.preventDefault();
      alert('Thanks! This demo form does not submit anywhere. Replace with your backend endpoint.');
    });
  </script>
</body>
</html>
