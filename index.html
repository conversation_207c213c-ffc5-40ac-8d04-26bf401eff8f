<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://use.typekit.net/oph7lmk.css"> <!-- Acumin Variable Concept via Adobe Fonts -->
  <link rel="stylesheet" href="nirmanah-styles.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <title>Nirmanah Design & Build</title>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- Navbar -->
  <nav class="flex items-center justify-between px-8 py-4 bg-white shadow-sm sticky top-0 z-50">
    <!-- Logo and Tagline -->
    <div class="flex items-center space-x-2">
      <img src="Logonirmanah.png" alt="Nirmanah Logo" class="w-10 h-10 object-contain">
      <div class="flex flex-col">
        <span class="text-xl text-gray-900 nirmanah-font">nirmanah</span>
        <span class="tagline">design & build</span>
      </div>
    </div>

    <!-- Nav Links Container -->
    <ul class="hidden md:flex items-center space-x-1 nav-links">
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">Home</a>
      </li>
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">About</a>
      </li>
      <li class="dropdown">
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm flex items-center"
           aria-haspopup="true" aria-expanded="false" role="button">
          Services
          <svg class="dropdown-arrow w-3 h-3 fill-current" viewBox="0 0 12 12">
            <path d="M6 8l4-4H2z"/>
          </svg>
        </a>
        <div class="dropdown-menu" role="menu">
          <a href="#" class="dropdown-item" role="menuitem">Design & Consultancy</a>
          <a href="#" class="dropdown-item" role="menuitem">Renovation & Remodeling</a>
          <a href="#" class="dropdown-item" role="menuitem">Landscaping & Outdoor Work</a>
          <a href="#" class="dropdown-item" role="menuitem">Turnkey Projects</a>
        </div>
      </li>
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">Portfolio</a>
      </li>
      <li>
        <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm">Contact</a>
      </li>
    </ul>

    <!-- Mobile Menu Button -->
    <div class="md:hidden">
      <button onclick="toggleMobileMenu()" class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200" aria-label="Toggle mobile menu">
        <svg id="menu-icon" class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
        <svg id="close-icon" class="w-6 h-6 text-gray-700 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Get a Quote Button -->
    <div class="hidden md:block">
      <button class="btn-primary">
        Get a Quote
      </button>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden bg-white border-b shadow-lg hidden">
    <div class="px-8 py-4 space-y-4">
      <a href="#" class="block py-2 text-primary font-medium border-l-4 border-primary pl-4 bg-secondary mobile-menu-link active">
        Home
      </a>
      <a href="#" class="block py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link">About</a>

      <!-- Mobile Services Dropdown -->
      <div class="mobile-dropdown">
        <button onclick="toggleMobileDropdown()" class="flex items-center justify-between w-full py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link" aria-expanded="false">
          Services
          <svg id="mobile-dropdown-arrow" class="w-4 h-4 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
          </svg>
        </button>
        <div id="mobile-dropdown-content" class="mobile-dropdown-content">
          <a href="#" class="block mobile-dropdown-item">Design & Consultancy</a>
          <a href="#" class="block mobile-dropdown-item">Renovation & Remodeling</a>
          <a href="#" class="block mobile-dropdown-item">Landscaping & Outdoor Work</a>
          <a href="#" class="block mobile-dropdown-item">Turnkey Projects</a>
        </div>
      </div>

      <a href="#" class="block py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link">Portfolio</a>
      <a href="#" class="block py-2 text-secondary hover:text-brand transition-colors duration-200 mobile-menu-link">Contact</a>
      <hr class="my-4">
      <div class="space-y-3">
        <button class="block w-full py-3 btn-primary">
          Get a Quote
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <main class="flex-1 flex items-center justify-center min-h-[calc(100vh-80px)]">
    <div class="text-center px-8">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">Welcome to Nirmanah</h1>
      <p class="text-xl text-gray-600 mb-8">Design & Build Excellence</p>
      <button class="btn-primary text-lg px-8 py-3">
        Get Started
      </button>
    </div>
  </main>

  <script>
    function toggleMobileMenu() {
      const mobileMenu = document.getElementById('mobile-menu');
      const menuIcon = document.getElementById('menu-icon');
      const closeIcon = document.getElementById('close-icon');

      if (mobileMenu.classList.contains('hidden')) {
        mobileMenu.classList.remove('hidden');
        menuIcon.classList.add('hidden');
        closeIcon.classList.remove('hidden');
      } else {
        mobileMenu.classList.add('hidden');
        menuIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
        // Close mobile dropdown when closing main menu
        const mobileDropdown = document.getElementById('mobile-dropdown-content');
        const mobileArrow = document.getElementById('mobile-dropdown-arrow');
        mobileDropdown.classList.remove('expanded');
        mobileArrow.style.transform = 'rotate(0deg)';
      }
    }

    function toggleMobileDropdown() {
      const dropdown = document.getElementById('mobile-dropdown-content');
      const arrow = document.getElementById('mobile-dropdown-arrow');
      const button = arrow.parentElement;

      if (dropdown.classList.contains('expanded')) {
        dropdown.classList.remove('expanded');
        arrow.style.transform = 'rotate(0deg)';
        button.setAttribute('aria-expanded', 'false');
      } else {
        dropdown.classList.add('expanded');
        arrow.style.transform = 'rotate(180deg)';
        button.setAttribute('aria-expanded', 'true');
      }
    }

    // Close mobile menu when window is resized to desktop
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768) {
        document.getElementById('mobile-menu').classList.add('hidden');
        document.getElementById('menu-icon').classList.remove('hidden');
        document.getElementById('close-icon').classList.add('hidden');

        // Reset mobile dropdown
        const mobileDropdown = document.getElementById('mobile-dropdown-content');
        const mobileArrow = document.getElementById('mobile-dropdown-arrow');
        mobileDropdown.classList.remove('expanded');
        mobileArrow.style.transform = 'rotate(0deg)';
      }
    });

    // Keyboard navigation for dropdown
    document.addEventListener('keydown', function(e) {
      const dropdown = document.querySelector('.dropdown');
      const dropdownMenu = document.querySelector('.dropdown-menu');

      if (e.key === 'Escape') {
        dropdownMenu.classList.remove('show');
        dropdown.querySelector('a').setAttribute('aria-expanded', 'false');
      }
    });

    // Handle dropdown focus for accessibility
    document.querySelectorAll('.dropdown > a').forEach(trigger => {
      trigger.addEventListener('focus', function() {
        this.setAttribute('aria-expanded', 'true');
        this.nextElementSibling.classList.add('show');
      });

      trigger.addEventListener('blur', function(e) {
        // Delay to check if focus moved to dropdown item
        setTimeout(() => {
          if (!this.nextElementSibling.contains(document.activeElement)) {
            this.setAttribute('aria-expanded', 'false');
            this.nextElementSibling.classList.remove('show');
          }
        }, 100);
      });
    });

    // Handle dropdown item focus
    document.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('blur', function(e) {
        const dropdown = this.closest('.dropdown');
        const trigger = dropdown.querySelector('a');

        setTimeout(() => {
          if (!dropdown.contains(document.activeElement)) {
            trigger.setAttribute('aria-expanded', 'false');
            dropdown.querySelector('.dropdown-menu').classList.remove('show');
          }
        }, 100);
      });
    });
  </script>
</body>
</html>
